package com.rs.module.ihc.controller.admin.mr;

import com.rs.framework.common.pojo.CommonResult;
import com.rs.framework.common.pojo.PageResult;
import com.rs.framework.common.util.object.BeanUtils;
import com.rs.module.ihc.controller.admin.mr.vo.MedicalRecordListReqVO;
import com.rs.module.ihc.controller.admin.mr.vo.MedicalRecordPageReqVO;
import com.rs.module.ihc.controller.admin.mr.vo.MedicalRecordRespVO;
import com.rs.module.ihc.controller.admin.mr.vo.MedicalRecordSaveReqVO;
import com.rs.module.ihc.entity.mr.MedicalRecordDO;
import com.rs.module.ihc.service.mr.MedicalRecordService;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiImplicitParam;
import io.swagger.annotations.ApiOperation;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.*;

import javax.annotation.Resource;
import javax.validation.Valid;
import java.util.List;

import static com.rs.framework.common.pojo.CommonResult.success;

@Api(tags = "病历管理")
@RestController
@RequestMapping("/ihc/mr/medicalRecord")
@Validated
public class MedicalRecordController {

    @Resource
    private MedicalRecordService medicalRecordService;

    @PostMapping("/create")
    @ApiOperation(value = "创建病历管理")
    public CommonResult<String> createMedicalRecord(@Valid @RequestBody MedicalRecordSaveReqVO createReqVO) {
        return success(medicalRecordService.createMedicalRecord(createReqVO));
    }

    @PostMapping("/update")
    @ApiOperation(value = "更新病历管理")
    public CommonResult<Boolean> updateMedicalRecord(@Valid @RequestBody MedicalRecordSaveReqVO updateReqVO) {
        medicalRecordService.updateMedicalRecord(updateReqVO);
        return success(true);
    }

    @GetMapping("/delete")
    @ApiOperation(value = "删除病历管理")
    @ApiImplicitParam(name = "ids", value = "编号")
    public CommonResult<Boolean> deleteMedicalRecord(@RequestParam("ids") String ids) {
        String[] strings = ids.split(",");
        for (String id : strings) {
           medicalRecordService.deleteMedicalRecord(id);
        }
        return success(true);
    }

    @GetMapping("/get")
    @ApiOperation(value = "获得病历管理")
    @ApiImplicitParam(name = "id", value = "编号")
    public CommonResult<MedicalRecordRespVO> getMedicalRecord(@RequestParam("id") String id) {
        MedicalRecordDO medicalRecord = medicalRecordService.getMedicalRecord(id);
        return success(BeanUtils.toBean(medicalRecord, MedicalRecordRespVO.class));
    }

    @PostMapping("/page")
    @ApiOperation(value = "获得病历管理分页")
    public CommonResult<PageResult<MedicalRecordRespVO>> getMedicalRecordPage(@Valid @RequestBody MedicalRecordPageReqVO pageReqVO) {
        PageResult<MedicalRecordDO> pageResult = medicalRecordService.getMedicalRecordPage(pageReqVO);
        return success(BeanUtils.toBean(pageResult, MedicalRecordRespVO.class));
    }

    @PostMapping("/list")
    @ApiOperation(value = "获得病历管理列表")
    public CommonResult<List<MedicalRecordRespVO>> getMedicalRecordList(@Valid @RequestBody MedicalRecordListReqVO listReqVO) {
        List<MedicalRecordDO> list = medicalRecordService.getMedicalRecordList(listReqVO);
        return success(BeanUtils.toBean(list, MedicalRecordRespVO.class));
    }

    //批量保存
    @PostMapping("/updateBatch")
    @ApiOperation(value = "批量更新病历管理")
    public CommonResult<Boolean> updateBatch(@Valid @RequestBody List<MedicalRecordSaveReqVO> updateReqList) {
        for (MedicalRecordSaveReqVO medicalRecordSaveReqVO : updateReqList) {
            medicalRecordService.updateMedicalRecord(medicalRecordSaveReqVO);
        }
        return success(true);
    }
}
